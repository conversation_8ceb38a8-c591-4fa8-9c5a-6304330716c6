package com.yzedulife.controller;

import com.yzedulife.annotation.Token;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.common.util.JwtUtil;
import com.yzedulife.convert.AnswerDetailAppConvert;
import com.yzedulife.convert.AnswerSheetAppConvert;
import com.yzedulife.response.AnswerDetailResponse;
import com.yzedulife.response.AnswerSheetResponse;
import com.yzedulife.response.Response;
import com.yzedulife.service.dto.*;
import com.yzedulife.service.service.*;
import com.yzedulife.util.SecurityUtil;
import com.yzedulife.vo.AnswerDetailVO;
import com.yzedulife.vo.AnswerSheetVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/answer")
@Tag(name = "答案模块")
public class AnswerController {

    @Autowired
    private AnswerSheetService answerSheetService;

    @Autowired
    private AnswerDetailService answerDetailService;

    @Autowired
    private QuestionOptionService questionOptionService;

    @Autowired
    private StudentUserService studentUserService;

    @Autowired
    private StudentClassService studentClassService;

    @Autowired
    private OtherUserService otherUserService;

    @Autowired
    private QuestionnaireService questionnaireService;

    @Token("student other")
    @Operation(summary = "创建答卷")
    @PostMapping("/create")
    public Response create(@Valid @RequestBody AnswerSheetVO answerSheetVO) {
        try {
            // 获取当前用户信息
            String token = SecurityUtil.getToken();
            String userType = JwtUtil.getType(token);
            String userId = JwtUtil.getId(token);

            // 验证问卷是否存在
            QuestionnaireDTO questionnaire = questionnaireService.getById(answerSheetVO.getQuestionnaireId());
            if (questionnaire == null) {
                return Response.error().msg("问卷不存在");
            }

            // 转换VO到DTO
            AnswerSheetDTO answerSheetDTO = AnswerSheetAppConvert.INSTANCE.vo2dto(answerSheetVO);
            answerSheetDTO.setSubmitterType(userType.toUpperCase());
            if ("student".equals(userType)) {
                answerSheetDTO.setStudentUserId(Long.parseLong(userId));
                // 验证学生是否存在
                StudentUserDTO student = studentUserService.getById(Long.parseLong(userId));
                if (student == null) {
                    return Response.error().msg("学生用户不存在");
                }
            } else if ("other".equals(userType)) {
                answerSheetDTO.setOtherUserId(Long.parseLong(userId));
                // 验证其他用户是否存在
                OtherUserDTO otherUser = otherUserService.getById(Long.parseLong(userId));
                if (otherUser == null) {
                    return Response.error().msg("用户不存在");
                }
            }
            answerSheetDTO.setSubmitTime(LocalDateTime.now());

            AnswerSheetDTO createdAnswerSheet = answerSheetService.create(answerSheetDTO);
            AnswerSheetResponse response = AnswerSheetAppConvert.INSTANCE.dto2response(createdAnswerSheet);

            return Response.success().data(response).msg("答卷创建成功");

        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("创建答卷失败", e);
            return Response.error().msg("创建答卷失败");
        }
    }

    @Token("student other")
    @Operation(summary = "提交答案")
    @PostMapping("/submit")
    @Transactional
    public Response submit(@RequestParam Long questionnaireId,
                          @Valid @RequestBody List<AnswerDetailVO> answerDetails) {
        try {
            // 获取当前用户信息
            String token = SecurityUtil.getToken();
            String userType = JwtUtil.getType(token);
            String userId = JwtUtil.getId(token);

            // 验证问卷是否存在
            QuestionnaireDTO questionnaire = questionnaireService.getById(questionnaireId);
            if (questionnaire == null) {
                return Response.error().msg("问卷不存在");
            }

            // 创建答卷
            AnswerSheetDTO answerSheetDTO = new AnswerSheetDTO();
            answerSheetDTO.setQuestionnaireId(questionnaireId);
            answerSheetDTO.setSubmitterType(userType.toUpperCase());
            answerSheetDTO.setSubmitTime(LocalDateTime.now());

            if ("student".equals(userType)) {
                answerSheetDTO.setStudentUserId(Long.parseLong(userId));
                // 验证学生是否存在
                StudentUserDTO student = studentUserService.getById(Long.parseLong(userId));
                if (student == null) {
                    return Response.error().msg("学生用户不存在");
                }
            } else if ("other".equals(userType)) {
                answerSheetDTO.setOtherUserId(Long.parseLong(userId));
                // 验证其他用户是否存在
                OtherUserDTO otherUser = otherUserService.getById(Long.parseLong(userId));
                if (otherUser == null) {
                    return Response.error().msg("用户不存在");
                }
            }

            AnswerSheetDTO createdAnswerSheet = answerSheetService.create(answerSheetDTO);

            // 提交答案详情
            List<AnswerDetailResponse> responseDetails = new ArrayList<>();
            for (AnswerDetailVO answerDetailVO : answerDetails) {
                // 验证选项代号是否存在
                List<QuestionOptionDTO> options = questionOptionService.getByQuestionId(answerDetailVO.getQuestionId());
                boolean optionExists = options.stream()
                        .anyMatch(option -> option.getOptionCode().equals(answerDetailVO.getChosenOptionCode()));

                if (!optionExists) {
                    throw new BusinessException("题目ID " + answerDetailVO.getQuestionId() + " 的选项代号 " + answerDetailVO.getChosenOptionCode() + " 不存在");
                }

                // 创建答案详情
                AnswerDetailDTO detailDTO = AnswerDetailAppConvert.INSTANCE.vo2dto(answerDetailVO);
                detailDTO.setAnswerSheetId(createdAnswerSheet.getId());

                AnswerDetailDTO createdDetail = answerDetailService.create(detailDTO);
                AnswerDetailResponse detailResponse = AnswerDetailAppConvert.INSTANCE.dto2response(createdDetail);
                responseDetails.add(detailResponse);
            }

            // 构建响应
            AnswerSheetResponse response = AnswerSheetAppConvert.INSTANCE.dto2response(createdAnswerSheet);
            response.setAnswers(responseDetails);

            return Response.success().data(response).msg("答案提交成功");

        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("提交答案失败", e);
            return Response.error().msg("提交答案失败");
        }
    }

    @Token("admin")
    @Operation(summary = "查看答案")
    @GetMapping("/list")
    public Response list(@RequestParam(required = false) Long questionnaireId) {
        try {
            List<AnswerSheetDTO> answerSheets;
            
            if (questionnaireId != null) {
                // 获取指定问卷的所有答卷
                answerSheets = answerSheetService.getByQuestionnaireId(questionnaireId);
            } else {
                // 获取所有答卷
                answerSheets = answerSheetService.getAll();
            }

            // 为每个答卷填充详细信息
            List<AnswerSheetResponse> responseList = new ArrayList<>();
            for (AnswerSheetDTO answerSheet : answerSheets) {
                AnswerSheetResponse response = AnswerSheetAppConvert.INSTANCE.dto2response(answerSheet);
                
                // 填充用户信息
                if (answerSheet.getStudentUserId() != null) {
                    try {
                        StudentUserDTO student = studentUserService.getById(answerSheet.getStudentUserId());
                        if (student != null) {
                            response.setStudentName(student.getName());
                            response.setStudentNumber(student.getStudentNumber());
                            response.setClassId(student.getClassId());

                            // 获取班级名称
                            if (student.getClassId() != null) {
                                try {
                                    StudentClassDTO studentClass = studentClassService.getById(student.getClassId());
                                    if (studentClass != null) {
                                        response.setClassName(studentClass.getClassName());
                                    }
                                } catch (Exception ex) {
                                    log.warn("获取班级信息失败：{}", student.getClassId());
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.warn("获取学生信息失败：{}", answerSheet.getStudentUserId());
                    }
                } else if (answerSheet.getOtherUserId() != null) {
                    try {
                        OtherUserDTO otherUser = otherUserService.getById(answerSheet.getOtherUserId());
                        if (otherUser != null) {
                            response.setPhone(otherUser.getPhone());
                        }
                    } catch (Exception e) {
                        log.warn("获取其他用户信息失败：{}", answerSheet.getOtherUserId());
                    }
                }

                // 填充问卷标题
                try {
                    QuestionnaireDTO questionnaire = questionnaireService.getById(answerSheet.getQuestionnaireId());
                    if (questionnaire != null) {
                        response.setQuestionnaireTitle(questionnaire.getTitle());
                    }
                } catch (Exception e) {
                    log.warn("获取问卷信息失败：{}", answerSheet.getQuestionnaireId());
                }

                // 获取答案详情
                List<AnswerDetailDTO> answerDetails = answerDetailService.getByAnswerSheetId(answerSheet.getId());
                List<AnswerDetailResponse> detailResponses = new ArrayList<>();
                for (AnswerDetailDTO detail : answerDetails) {
                    AnswerDetailResponse detailResponse = AnswerDetailAppConvert.INSTANCE.dto2response(detail);
                    detailResponses.add(detailResponse);
                }
                response.setAnswers(detailResponses);

                responseList.add(response);
            }

            return Response.success().data(responseList);

        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("查看答案失败", e);
            return Response.error().msg("查看答案失败");
        }
    }
}
